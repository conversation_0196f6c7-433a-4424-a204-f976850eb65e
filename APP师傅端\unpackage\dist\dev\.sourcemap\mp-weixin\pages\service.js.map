{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/service.vue?03df", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/service.vue?0225", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/service.vue?a955", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/service.vue?7409", "uni-app:///pages/service.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/service.vue?4c97", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/service.vue?2b45"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "tabbar", "data", "showLargePromoPopup", "showActivityPopup", "showConfirmPopup", "showLoginPopup", "list1", "bannerList", "text1", "huodongdata", "showmsg", "district", "position", "baseList", "service", "couponList", "isLoading", "isRefreshing", "isServiceLoading", "isPopupShrunk", "scrollTimeout", "isPageLoaded", "onLoad", "scene", "console", "uni", "isLargePromoClosed", "onPullDownRefresh", "onShow", "autograph", "onShareAppMessage", "onPageScroll", "clearTimeout", "methods", "closeLargePromoPopup", "grabDeal", "closeActivityPopup", "showConfirm", "closeConfirmPopup", "closeLoginPopup", "goToLogin", "url", "fail", "title", "icon", "confirmToHuodongParity", "duration", "<PERSON><PERSON><PERSON>u", "couponId", "haveGet", "debounced<PERSON><PERSON>ling<PERSON><PERSON>", "getBottom", "initData", "Promise", "getcount", "gethuodongconfig", "<PERSON><PERSON><PERSON>", "getSearchList", "res", "getCoupon", "userId", "goUrl", "copyMethod", "res4", "getNowPosition", "type", "isHighAccuracy", "accuracy", "success", "res1", "city_id", "key", "val", "resolve", "computed", "primaryColor", "subColor", "configInfo", "commonOptions", "userInfo", "userPageType", "mineInfo", "serviceCateData"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,yTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChDA;AAAA;AAAA;AAAA;AAAs1B,CAAgB,s2BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACyL12B;AAMA;AAEA;AAAA;AAAA;EAAA;IAAA;EAAA,CAAC;AAAD;AAAA,eAEA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cACAC;cACAC;cACA;gBACA;gBACAC;gBACAD;cACA;gBACAA;cACA;cACA;cACAE;cACA;gBACA;cACA;cAAA;cAAA,OACA;YAAA;cAAA;cAAA,OACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EACAC;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cACAH;cAAA,KACA;gBAAA;gBAAA;cAAA;cACAC;cAAA;YAAA;cAGA;cAAA;cAAA;cAAA,OAEA;YAAA;cAAA;cAAA,OACA;YAAA;cAAA;cAAA;YAAA;cAAA;cAAA;cAEAD;YAAA;cAAA;cAEA;cACAC;cAAA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EAEA;EACAG;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cACAC;cAAA,MACAA;gBAAA;gBAAA;cAAA;cAAA;cAAA,OACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EAEA;EACAC;EACAC;IAAA;IACA;MACA;IACA;IACA;MACAC;IACA;IACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACA;MACAT;IACA;IACAU;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MACA;IACA;IACAC;MAAA;MACA;MACA;MACAf;QACAgB;QACAC;UACAlB;UACAC;YACAkB;YACAC;UACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;QACA;QACA;MACA;;MAEA;MACA;MACA;MACA;MACApB;MACA;QACA;MACA;QACAA;UACAmB;UACAD;UACAG;QACA;MACA;IACA;EAAA,GACA,yCACA;IACAC;MAAA;MACA;QACAtB;UACAmB;UACAD;UACAG;QACA;QACA;MACA;MACA;QACAE;MACA;MACA;QACAxB;QACA;UACAC;YACAmB;YACAD;YACAG;UACA;UACA;YACA;cACA;gBAAAG;cAAA;YACA;YACA;UACA;UACA;QACA;UACAxB;YACAmB;YACAD;YACAG;UACA;QACA;MACA;QACAtB;QACAC;UACAmB;UACAD;UACAG;QACA;MACA;IACA;IACAI;MACA;IACA;IACAC;MAAA;MACA;MACA;QACA;UACA;QACA;UACA3B;UACA;UACAC;YACAkB;YACAC;UACA;QACA;MACA;QACApB;QACA;QACAC;UACAkB;UACAC;QACA;MACA;QACA;MACA;IACA;IACAQ;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBACA;gBAAA;gBAAA;gBAAA,OAEAC,aACA,qBACA,2BACA,yBACA,mBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA7B;gBACAC;kBACAkB;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAU;MAAA;MACA;QACA;UACA;UACA;QACA;UACA;QACA;QACA9B;MACA;IACA;IACA+B;MAAA;MACA;QACA;UACA;UACA;UACA;UACA/B;QACA;QACAA;MACA;IACA;IACAgC;MACA;QACA/B;UACAkB;UACAC;QACA;QACA;MACA;MACAnB;QACAgB;QACAC;UACAlB;UACAC;YACAkB;YACAC;UACA;QACA;MACA;IACA;IACAa;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAlC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAmC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAC;gBAAA;gBAAA,OACA;kBACAA;gBACA;cAAA;gBAFAF;gBAGA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAlC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAqC;MACApC;QACAgB;QACAC;UACAlB;QACA;MACA;IACA;IACAsC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACA;gBACA;kBAAA;gBAAA;gBACA;gBACA;kBAAA;gBAAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAvC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAwC;MAAA;MACA;QACAvC;UACAwC;UACAC;UACAC;UACAC;YACA3C;YACAA;YACAA;cACAgB;cACA2B;gBACA5C;gBACA;gBACAA;gBACA;gBACA,mFACA6C;gBACA5C;kBACA6C;kBACA1D;gBACA;gBACA;kBACA2D;kBACAC;gBACA;gBACAC;cACA;cACA/B;gBACAlB;gBACAiD;cACA;YACA;UACA;UACA/B;YACAlB;YACAiD;UACA;QACA;MACA;IACA;EAAA,EACA;EACAC,4BACA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACArE;MAAA;IAAA;IACAsE;MAAA;IAAA;EACA;AAEA;AAAA,2B;;;;;;;;;;;;;ACjjBA;AAAA;AAAA;AAAA;AAA6lD,CAAgB,ijDAAG,EAAC,C;;;;;;;;;;;ACAjnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/service.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './pages/service.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./service.vue?vue&type=template&id=fe340868&scoped=true&\"\nvar renderjs\nimport script from \"./service.vue?vue&type=script&lang=js&\"\nexport * from \"./service.vue?vue&type=script&lang=js&\"\nimport style0 from \"./service.vue?vue&type=style&index=0&id=fe340868&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"fe340868\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/service.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./service.vue?vue&type=template&id=fe340868&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uSwiper: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-swiper/u-swiper\" */ \"uview-ui/components/u-swiper/u-swiper.vue\"\n      )\n    },\n    uNoticeBar: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-notice-bar/u-notice-bar\" */ \"uview-ui/components/u-notice-bar/u-notice-bar.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./service.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./service.vue?vue&type=script&lang=js&\"", "<template>\n<view class=\"page\" ref=\"abc\">\n  <!-- Modified: Added confirm popup with page load condition -->\n  <view class=\"confirm-popup-overlay\" v-if=\"showConfirmPopup && isPageLoaded\">\n    <view class=\"confirm-popup-container\">\n      <view class=\"confirm-popup-content\">\n        <view class=\"confirm-title\">活动规则</view>\n        <view class=\"confirm-text\">\n          1.本活动仅限安徽省阜阳市临泉县地区用户参与（以系统验证的定位信息或收货地址为准）<br>\n          2. 活动时间：{{huodongdata.startTime}}，结束时间{{huodongdata.endTime}}（北京时间），仅限在此时间段内完成支付的订单可参与<br>\n          3. 本次活动仅适用于<text style=\"color:#E8260D ;\">挂式空调清洗服务（不含中央空调、柜式空调等其他类型）</text>，服务包含标准内机清洁<br>\n          4. 每位用户限参与{{huodongdata.maxCount}}次（按用户手机号、支付账号和设备信息识别），服务排期将按支付时间顺序安排，支付成功后服务师傅将在48小时内跟您联系，请注意来电提醒。<br>\n          5. 参与活动订单不可使用任何优惠券（专属活动优惠券除外），使用优惠券的订单将自动取消<br>\n          6. 禁止通过虚假定位、技术刷单等非正常手段参与，违规订单将视为无效并取消服务资格，用户可在7个工作日内申诉\t<br>\n          7. 在法律允许范围内，平台保留对活动规则的最终解释及调整权利\n        </view>\n        <view class=\"confirm-buttons\">\n          <view class=\"confirm-btn cancel\" @click=\"closeConfirmPopup\">取消</view>\n          <view class=\"confirm-btn confirm\" @click=\"confirmToHuodongParity\">确认</view>\n        </view>\n      </view>\n    </view>\n  </view>\n\n  <!-- Added: Login prompt popup -->\n  <view class=\"confirm-popup-overlay\" v-if=\"showLoginPopup\">\n    <view class=\"confirm-popup-container\">\n      <view class=\"confirm-popup-content\">\n        <view class=\"confirm-title\">登录提示</view>\n        <view class=\"confirm-text\">\n          您尚未登录，请先登录以继续参与活动。\n        </view>\n        <view class=\"confirm-buttons\">\n          <view class=\"confirm-btn cancel\" @click=\"closeLoginPopup\">取消</view>\n          <view class=\"confirm-btn confirm\" @click=\"goToLogin\">确认</view>\n        </view>\n      </view>\n    </view>\n  </view>\n  \n  <!-- Modified: Added page load condition -->\n  <view class=\"large-promo-overlay\" v-if=\"showLargePromoPopup && district === '临泉县' && isPageLoaded\">\n    <view class=\"large-promo-container\">\n      <image :src=\"huodongdata.sharePictures\" class=\"promo-ac\" mode=\"widthFix\" />\n      <!--  <view class=\"promo-background-area\">\n        <image src=\"../static/images/huodong.png\" class=\"promo-ac\" mode=\"widthFix\" />\n      </view> -->\n      \n      <view class=\"promo-foreground-area\">\n        <view class=\"promo-price\">\n        <!--  <text class=\"price-val\">{{huodongdata.payPrice}}</text>\n          <text class=\"price-unit\">元!!!</text> -->\n        </view>\n        <!-- <view class=\"promo-subtitle\">\n          空调清洗秒杀中!\n        </view> -->\n      </view>\n      <view class=\"promo-button-area\" @click=\"showConfirm\">\n        <image src=\"../static/images/yuyue.png\" mode=\"widthFix\" class=\"button-image\" />\n        <view class=\"hand-pointer-animation\">\n          <image src=\"/static/images/promo_hand.png\" mode=\"aspectFit\" class=\"hand-pointer-img\" />\n        </view>\n      </view>\n      <view class=\"promo-close-btn\" @click=\"closeLargePromoPopup\">\n        <uni-icons type=\"closeempty\" color=\"#fff\" size=\"18\"></uni-icons>\n      </view>\n    </view>\n  </view>\n  \n  <!-- Modified: Adjusted transition and shrink behavior with page load condition -->\n  <view \n    class=\"activity-popup\" \n    v-if=\"showActivityPopup && district === '临泉县' && isPageLoaded\" \n    @click=\"showConfirm\"\n    :class=\"{ 'activity-popup-shrunk': isPopupShrunk }\"\n    :style=\"{ transform: isPopupShrunk ? 'translateX(60rpx)' : 'translateX(0)' }\"\n  >\n    <view class=\"close-btn\" @click.stop=\"closeActivityPopup\">×</view>\n    <view class=\"popup-content\">\n      <view class=\"popup-text-main\">空调清洗</view>\n      <image src=\"../static/images/kongtiao.png\" class=\"popup-header-image\" mode=\"aspectFit\"></image>\n      <view class=\"popup-text-price\">\n        <text class=\"price-number\">{{huodongdata.payPrice}}</text>秒杀\n      </view>\n      <view class=\"popup-action-btn\">\n        立即抢\n      </view>\n    </view>\n  </view>\n  <tabbar :cur=\"0\"></tabbar>\n  <view class=\"content\">\n    <view style=\"display: flex; align-items: center; justify-content: space-between;\">\n      <image lazy-load src=\"../static/images/logo-index.jpg\" mode=\"aspectFit\"\n        style=\"width: 50rpx; height: 50rpx; border-radius: 20%;\"></image>\n      <view class=\"search_position\">\n        <uni-icons type=\"location-filled\" size=\"20\"></uni-icons>\n        <view class=\"position\">{{ position }}</view>\n        <u-icon name=\"arrow-down-fill\" color=\"#ADADAD\" size=\"8\"></u-icon>\n        <view class=\"shu\">丨</view>\n        <uni-icons type=\"search\" size=\"20\" color=\"#ADADAD\"></uni-icons>\n        <input type=\"text\" placeholder=\"空调维修\" @focus=\"goUrl('/user/search')\">\n        <view class=\"btn\" @click=\"goUrl('/user/search')\">搜索</view>\n      </view>\n    </view>\n    <view class=\"img\">\n      <u-swiper :list=\"list1\" height=\"108\" :lazy-load=\"true\"></u-swiper>\n    </view>\n    <view class=\"tag\">\n      <view class=\"tag_item\"><text>就近师傅</text></view>丨\n      <view class=\"tag_item\"><text>准时上门</text></view>丨\n      <view class=\"tag_item\"><text>满意为止</text></view>丨\n      <view class=\"tag_item\"><text>30天保修</text></view>\n    </view>\n    <view class=\"grid\">\n      <view class=\"grid-container\">\n        <view class=\"grid-item\" v-for=\"(baseListItem, baseListIndex) in baseList\" :key=\"baseListIndex\"\n          @click=\"goUrl(baseListItem.link)\">\n          <image lazy-load :src=\"baseListItem.img\" mode=\"aspectFit\"\n            style=\"width: 96rpx; height: 94rpx; border-radius: 50%;\"></image>\n          <text class=\"grid-text\">{{ baseListItem.title }}</text>\n        </view>\n      </view>\n    </view>\n    <view class=\"swiper\" id=\"id\">\n      <span>公<text>告</text></span>\n      <view class=\"shu\">丨</view>\n      <u-notice-bar :text=\"text1\" url=\"\" direction=\"column\" bgColor=\"#fff\" color=\"#999\" :icon=\"null\"\n        fontSize=\"12\"></u-notice-bar>\n    </view>\n    <view class=\"welfare\">\n      <view class=\"top\">\n        <view class=\"left\">\n          <image lazy-load src=\"../static/images/4206.png\" mode=\"aspectFit\"\n            style=\"width: 46rpx; height: 46rpx;\"></image>\n          <text>限时福利,先到先得</text>\n        </view>\n        <!-- Modified: Moved btn back inside welfare.top -->\n        <view class=\"btn\" @tap=\"goUrl('/pages/welfare')\">查看更多<u-icon name=\"play-right-fill\" color=\"#fff\"\n          size=\"8\"></u-icon>\n        </view>\n      </view>\n      <view class=\"bottom\">\n        <image lazy-load src=\"../static/images/9467.png\" mode=\"aspectFit\"\n          style=\"width: 170rpx; height: 224rpx;\"></image>\n        <view class=\"right\">\n          <view class=\"right_item\" v-for=\"(item, index) in couponList\" :key=\"index\">\n            <view class=\"box1\"><span>￥</span>{{ item.discount }}元</view>\n            <view class=\"box2\">{{ item.full == 0 ? '通用券' : '满减券' }}</view>\n            <view class=\"box3\" @click=\"debouncedGetlingqu(item)\"\n              :style=\"item.haveGet == 1 ? 'background:#F2F3F4; color:#ADADAD;' : ''\">\n              {{ item.haveGet == 1 ? '已领取' : '立即领取' }}\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n    <view class=\"service\" v-for=\"(newItem, newIndex) in service\" :key=\"newIndex\">\n      <view class=\"head\">\n        <view class=\"left\">{{ newItem.name }}</view>\n        <view class=\"right\" @click=\"goUrl(`/pages/technician?id=${newItem.id}`)\">\n          查看更多\n          <u-icon name=\"arrow-right\" color=\"#999999\" size=\"12\"></u-icon>\n        </view>\n      </view>\n      <view class=\"se_main\">\n        <view class=\"se_item\" v-for=\"(item, index) in newItem.serviceList\" :key=\"index\"\n          @click=\"goUrl(`/user/commodity_details?id=${item.id}`)\">\n          <image lazy-load :src=\"item.cover\" mode=\"aspectFit\"\n            style=\"width: 290rpx; height: 286rpx; border-radius: 16rpx;\"></image>\n          <view class=\"lbox\">\n            <view class=\"name\">{{ item.title }}</view>\n            <view class=\"baojia\">师傅报价</view>\n          </view>\n        </view>\n      </view>\n    </view>\n    <view style=\"margin-top: 20rpx;\" class=\"\">\n      <view class=\"tips\">家庭应急维修 首选今师傅</view>\n      <view class=\"tips2\">200万+专业师傅 100万+附近服务网点 覆盖全国城市村镇</view>\n    </view>\n  </view>\n</view>\n</template>\n\n<script>\nimport {\n  mapState,\n  mapActions,\n  mapMutations\n} from \"vuex\";\nimport tabbar from \"@/components/tabbar.vue\";\nimport {\n  debounce\n} from \"lodash\";\n\nexport default {\n  components: {\n    tabbar,\n  },\n  data() {\n    return {\n      showLargePromoPopup: true,\n      showActivityPopup: true,\n      showConfirmPopup: false,\n      showLoginPopup: false, // Added: For login prompt popup\n      list1: [],\n      bannerList: [],\n      text1: [],\n      huodongdata: '',\n      showmsg: '',\n      district: '',\n      position: \"\",\n      baseList: [],\n      service: [],\n      couponList: [],\n      isLoading: false,\n      isRefreshing: false,\n      isServiceLoading: false,\n      isPopupShrunk: false,\n      scrollTimeout: null,\n      isPageLoaded: false,\n    };\n  },\n  async onLoad(query) {\n    const scene = decodeURIComponent(query.scene || '');\n    console.log('开始获取 scene:', scene);\n    if (scene) {\n      this.$store.commit('setErweima', scene);\n      uni.setStorageSync('erweima', scene);\n      console.log('已存储 scene:', scene);\n    } else {\n      console.log('未获取到 scene 参数');\n    }\n    // Check if large-promo-overlay was previously closed\n    const isLargePromoClosed = uni.getStorageSync('largePromoClosed');\n    if (isLargePromoClosed) {\n      this.showLargePromoPopup = false;\n    }\n    await this.initData();\n    await this.getBottom();\n  },\n  async onPullDownRefresh() {\n    console.log('开始下拉刷新');\n    if (this.isRefreshing) {\n      uni.stopPullDownRefresh();\n      return;\n    }\n    this.isRefreshing = true;\n    try {\n      await this.initData();\n      await this.getBottom();\n    } catch (error) {\n      console.error('刷新失败:', error);\n    } finally {\n      this.isRefreshing = false;\n      uni.stopPullDownRefresh();\n    }\n  },\n  async onShow() {\n    const autograph = uni.getStorageSync(\"autograph\");\n    if (autograph && autograph !== \"\") {\n      await this.getSearchList();\n    }\n  },\n  onShareAppMessage() {},\n  onPageScroll(e) {\n    if (!this.isPopupShrunk) {\n      this.isPopupShrunk = true;\n    }\n    if (this.scrollTimeout) {\n      clearTimeout(this.scrollTimeout);\n    }\n    this.scrollTimeout = setTimeout(() => {\n      this.isPopupShrunk = false;\n    }, 3000);\n  },\n  methods: {\n    closeLargePromoPopup() {\n      this.showLargePromoPopup = false;\n      // Save the closed state to local storage\n      uni.setStorageSync('largePromoClosed', true);\n    },\n    grabDeal() {\n      this.showConfirm();\n    },\n    closeActivityPopup() {\n      this.showActivityPopup = false;\n    },\n    showConfirm() {\n      this.showConfirmPopup = true;\n    },\n    closeConfirmPopup() {\n      this.showConfirmPopup = false;\n    },\n    closeLoginPopup() { // Added: Close login popup\n      this.showLoginPopup = false;\n    },\n    goToLogin() { // Added: Navigate to login page\n      this.showLoginPopup = false;\n      this.showConfirmPopup = false;\n      uni.navigateTo({\n        url: '/pages/mine',\n        fail: (err) => {\n          console.error(\"导航到登录页面失败:\", err);\n          uni.showToast({\n            title: \"跳转失败\",\n            icon: \"none\",\n          });\n        },\n      });\n    },\n    confirmToHuodongParity() {\n      // Check if user is logged in by retrieving token\n      const token = uni.getStorageSync('token');\n      if (!token || token === '') {\n        this.showLoginPopup = true;\n        return;\n      }\r\n\t    \n      // Existing logic if token exists\n      this.showConfirmPopup = false;\n      this.showLargePromoPopup = false;\n      // Save the closed state to local storage when confirming\n      uni.setStorageSync('largePromoClosed', true);\n      if(this.isPageLoaded){\n        this.goUrl('/user/huodong_parity?id=519&type=1');\n      }else{\n        uni.showToast({\n          icon: 'none',\n          title: this.showmsg,\n          duration: 1000\n        });\n      }\n    },\n    ...mapActions([\"serviceCate\"]),\n    ...mapMutations([\"updatePosition\"]),\n    getlingqu(item) {\n      if (item.haveGet == 1) {\n        uni.showToast({\n          icon: 'none',\n          title: '已领取过了',\n          duration: 1000\n        });\n        return;\n      }\n      const obj = {\n        couponId: [item.id]\n      };\n      this.$api.service.getWelfare(obj).then(res => {\n        console.log(res)\n        if (res.code === \"200\") {\n          uni.showToast({\n            icon: 'success',\n            title: '领取成功',\n            duration: 1000\n          });\n          this.couponList = this.couponList.map(coupon => {\n            if (coupon.id === item.id) {\n              return { ...coupon, haveGet: 1 };\n            }\n            return coupon;\n          });\n          this.getCoupon();\n        } else {\n          uni.showToast({\n            icon: 'error',\n            title: '领取失败',\n            duration: 1000\n          });\n        }\n      }).catch(err => {\n        console.error(\"领取优惠券失败:\", err);\n        uni.showToast({\n          icon: 'error',\n          title: err,\n          duration: 1000\n        });\n      });\n    },\n    debouncedGetlingqu: debounce(function(item) {\n      this.getlingqu(item);\n    }, 1000),\n    getBottom() {\n      this.isServiceLoading = true;\n      return this.$api.service.getBottom(\"阜阳市\").then(res => {\n        if (res && Array.isArray(res)) {\n          this.service = res;\n        } else {\n          console.warn(\"getBottom 返回数据无效或为空:\", res);\n          this.service = [];\n          uni.showToast({\n            title: \"服务数据加载失败\",\n            icon: \"none\",\n          });\n        }\n      }).catch(err => {\n        console.error(\"获取底部数据失败:\", err);\n        this.service = [];\n        uni.showToast({\n          title: \"服务数据加载失败\",\n          icon: \"none\",\n        });\n      }).finally(() => {\n        this.isServiceLoading = false;\n      });\n    },\n    async initData() {\n      if (this.isLoading) return;\n      this.isLoading = true;\n      try {\n        await Promise.all([\n          this.copyMethod(),\n          this.gethuodongconfig(),\n          this.getNowPosition(),\n          this.getCoupon(),\n        ]);\n      } catch (err) {\n        console.error(\"初始化数据失败:\", err);\n        uni.showToast({\n          title: \"数据加载失败\",\n          icon: \"none\",\n        });\n      } finally {\n        this.isLoading = false;\n      }\n    },\n    getcount(){\n      this.$api.service.huodongcount().then(res=>{\n        if(res.code===\"-1\"){\n          this.isPageLoaded = false; \n          this.showmsg=res.msg\n        }else{\n          this.isPageLoaded = true; \n        }\n        console.log(res)\n      })  \n    },\n    gethuodongconfig(){\n      this.$api.service.huodongselectActivityConfig().then(res=>{\n        if(res.code===\"200\"){\n        this.getcount()\n          this.huodongdata=res.data\n          this.isPageLoaded = true; \n          console.log(this.huodongdata)\n        }\n        console.log(res)\n      })\n    },\n    godetils(id) {\n      if (!id) {\n        uni.showToast({\n          title: \"无效的商品 ID\",\n          icon: \"none\",\n        });\n        return;\n      }\n      uni.navigateTo({\n        url: `/pages/details/details?id=${id}`,\n        fail: (err) => {\n          console.error(\"跳转失败:\", err);\n          uni.showToast({\n            title: \"跳转失败: \" + err.errMsg,\n            icon: \"none\",\n          });\n        },\n      });\n    },\n    async getSearchList() {\n      try {\n        const res = await this.$api.service.getHotSearch();\n        this.SearchList = res.length > 3 ? res.slice(0, 3) : res;\n      } catch (err) {\n        console.error(\"获取热搜失败:\", err);\n      }\n    },\n    async getCoupon() {\n      try {\n        let userId = uni.getStorageSync('userId');\n        const res = await this.$api.service.getWelfareList({\n          userId: userId\n        });\n        this.couponList = res.list.slice(0, 3);\n      } catch (err) {\n        console.error(\"获取优惠券失败:\", err);\n      }\n    },\n    goUrl(e) {\n      uni.navigateTo({\n        url: e,\n        fail: (err) => {\n          console.error(\"导航失败:\", err);\n        },\n      });\n    },\n    async copyMethod() {\n      try {\n        const res4 = await this.$api.service.index();\n        this.bannerList = res4.banner;\n        this.list1 = res4.banner.map((item) => item.img);\n        this.baseList = res4.jingang;\n        this.text1 = res4.notices.map((item) => item.content);\n        this.service = res4.serviceCate;\n      } catch (err) {\n        console.error(\"获取首页数据失败:\", err);\n      }\n    },\n    getNowPosition() {\n      return new Promise((resolve) => {\n        uni.getLocation({\n          type: \"gcj02\",\n          isHighAccuracy: true,\n          accuracy: \"best\",\n          success: (res) => {\n            uni.setStorageSync(\"lat\", res.latitude);\n            uni.setStorageSync(\"lng\", res.longitude);\n            uni.request({\n              url: `https://restapi.amap.com/v3/geocode/regeo?key=4272f5716dfd17882409f306c0299666&location=${res.longitude},${res.latitude}`,\n              success: (res1) => {\n                console.log(res1)\n                this.district = res1.data.regeocode.addressComponent.district\n                console.log(this.district)\n                const province = res1.data.regeocode.addressComponent.province;\n                this.position = typeof res1.data.regeocode.addressComponent.city === \"string\" ?\n                  res1.data.regeocode.addressComponent.city : province;\n                uni.setStorageSync(\"city\", {\n                  city_id: res1.data.regeocode.addressComponent.adcode,\n                  position: this.position,\n                });\n                this.updatePosition({\n                  key: \"position\",\n                  val: this.position,\n                });\n                resolve();\n              },\n              fail: (err) => {\n                console.error(\"逆地理编码失败:\", err);\n                resolve();\n              },\n            });\n          },\n          fail: (err) => {\n            console.error(\"获取定位失败:\", err);\n            resolve();\n          },\n        });\n      });\n    },\n  },\n  computed: {\n    ...mapState({\n      primaryColor: (state) => state.config.configInfo.primaryWidth,\n      subColor: (state) => state.config.configInfo.subColor,\n      configInfo: (state) => state.config.configInfo,\n      commonOptions: (state) => state.user.commonOptions,\n      userInfo: (state) => state.user.userInfo,\n      userPageType: (state) => state.user.userPageType,\n      mineInfo: (state) => state.user.mineInfo,\n      position: (state) => state.position,\n      serviceCateData: (state) => state.serviceCateData,\n    }),\n  },\n};\n</script>\n\n<style scoped lang=\"scss\">\n/* Added: Styles for confirm popup */\n.confirm-popup-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100vw;\n  height: 100vh;\n  background-color: rgba(0, 0, 0, 0.7);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 1002;\n  transition: opacity 0.3s ease-in-out;\n}\n\n.confirm-popup-container {\n  width: 600rpx;\n  background: #fff;\n  border-radius: 20rpx;\n  padding: 40rpx;\n  box-sizing: border-box;\n}\n\n.confirm-popup-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.confirm-title {\n  font-size: 36rpx;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 20rpx;\n}\n\n.confirm-text {\n  font-size: 28rpx;\n  color: #666;\n  line-height: 40rpx;\n  text-align: left;\n  width: 100%;\n  margin-bottom: 40rpx;\n}\n\n.confirm-buttons {\n  display: flex;\n  justify-content: space-between;\n  width: 100%;\n}\n\n.confirm-btn {\n  width: 200rpx;\n  height: 80rpx;\n  line-height: 80rpx;\n  text-align: center;\n  border-radius: 40rpx;\n  font-size: 28rpx;\n}\n\n.confirm-btn.cancel {\n  background: #f2f3f4;\n  color: #666;\n}\n\n.confirm-btn.confirm {\n  background: linear-gradient(270deg, #EA5533 0%, #EE8751 100%);\n  color: #fff;\n}\n\n/* Existing styles */\n.large-promo-overlay,\n.activity-popup {\n  transition: opacity 0.3s ease-in-out;\n}\n.large-promo-overlay[hidden],\n.activity-popup[hidden] {\n  opacity: 0;\n  pointer-events: none;\n}\n\n.content {\n  transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;\n}\n.content[hidden] {\n  opacity: 0;\n  transform: translateY(20rpx);\n}\n\n@keyframes hand-press {\n  0% {\n    transform: scale(1);\n  }\n  50% {\n    transform: scale(0.9);\n  }\n  100% {\n    transform: scale(1);\n  }\n}\n\n.pulse-text {\n  animation: pulse-animation 0.5s infinite ease-in-out;\n}\n\n.large-promo-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100vw;\n  height: 100vh;\n  background-color: rgba(0, 0, 0, 0.7);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 1001;\n}\n\n.large-promo-container {\n  position: relative;\n  width: 600rpx;\n  height: 480rpx;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.promo-background-area {\n  position: absolute;\n  top: 0;\n  width: 100%;\n  height: 252rpx;\n  background: radial-gradient(circle at 50% 120%, #ff8964, #dd4a34);\n  border-radius: 14rpx;\n}\n\n.promo-ac {\n  position: absolute;\n  top: -100rpx;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 450rpx;\n  z-index: 1;\n}\n\n.promo-character {\n  position: absolute;\n  bottom: -6rpx;\n  right: 18rpx;\n  width: 132rpx;\n  z-index: 3;\n}\n\n.promo-coin {\n  position: absolute;\n  bottom: 72rpx;\n  left: 24rpx;\n  width: 54rpx;\n  z-index: 3;\n}\n\n.promo-foreground-area {\n  position: absolute;\n  top: 200rpx;\n  width: 348rpx;\n  height: 180rpx;\n  border-radius: 24rpx;\n  z-index: 2;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding-top: 24rpx;\n  box-sizing: border-box;\n}\n\n.promo-price {\n  color: #f82c28;\n  font-weight: 900;\n  display: flex;\n  align-items: baseline;\n  line-height: 1;\n  text-shadow: -1px -1px 0 #fff, 1px -1px 0 #fff, -1px 1px 0 #fff, 1px 1px 0 #fff;\n}\n\n.price-val {\n  font-size: 96rpx;\n}\n\n.price-unit {\n  font-size: 30rpx;\n  margin-left: 6rpx;\n}\n\n.promo-subtitle {\n  margin-top: 12rpx;\n  background-color: #fadda6;\n  color: #f82c28;\n  font-size: 22rpx;\n  font-weight: bold;\n  padding: 5rpx 18rpx;\n  border-radius: 18rpx;\n}\n\n.promo-button-area {\n  position: absolute;\n  bottom: -10rpx;\n  z-index: 5;\n  width: 450rpx;\n  height: 60rpx;\n  border-radius: 30rpx;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  color: #fff;\n  font-size: 26rpx;\n  font-weight: bold;\n  text-shadow: 0 1rpx 2rpx rgba(0,0,0,0.3);\n  cursor: pointer;\n  animation: pulse-animation 0.5s infinite ease-in-out;\n}\n\n.button-image {\n  width: 320rpx !important;\n  height: 100%;\n}\n\n.hand-pointer-animation {\n  position: absolute;\n  right: 30rpx;\n  bottom: -12rpx;\n  animation: hand-press 1.5s infinite;\n}\n\n.hand-pointer-img {\n  width: 48rpx;\n  height: 48rpx;\n}\n\n.promo-close-btn {\n  position: absolute;\n  bottom: -80rpx;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 42rpx;\n  height: 42rpx;\n  border: 2rpx solid rgba(255,255,255,0.7);\n  border-radius: 50%;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 4;\n}\n\n@keyframes pulse-animation {\n  0% {\n    transform: scale(1);\n  }\n  50% {\n    transform: scale(1.05);\n  }\n  100% {\n    transform: scale(1);\n  }\n}\n\n.activity-popup {\n  position: fixed;\n  right: 16rpx;\n  bottom: 600rpx;\n  z-index: 999;\n  width: 120rpx;\n  background: linear-gradient(180deg, #FFE1C1 0%, #FF7B5A 100%);\n  border-radius: 12rpx;\n  border: 2rpx solid #FFF;\n  box-shadow: 0rpx 4rpx 8rpx rgba(0, 0, 0, 0.2);\n  padding: 6rpx;\n  cursor: pointer;\n  transition: transform 0.3s ease-in-out;\n}\n\n.activity-popup-shrunk {\n}\n\n.activity-popup-shrunk .popup-content {\n  opacity: 1;\n}\n\n.popup-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  transition: opacity 0.3s ease-in-out;\n}\n\n.popup-header-image {\n  width: 70rpx;\n  height: 40rpx;\n  margin-top: 4rpx;\n}\n\n.popup-text-main {\n  font-size: 20rpx;\n  color: #A34A00;\n  font-weight: bold;\n  margin-top: 6rpx;\n}\n\n.popup-text-price {\n  font-size: 18rpx;\n  color: #E53935;\n  margin-top: 2rpx;\n  font-weight: 500;\n  white-space: nowrap;\n}\n\n.price-number {\n  font-size: 24rpx;\n  font-weight: bold;\n}\n\n.popup-action-btn {\n  width: 90rpx;\n  margin-top: 8rpx;\n  margin-bottom: 4rpx;\n  padding: 6rpx 0;\n  background: linear-gradient(270deg, #FF5A36 0%, #F60100 100%);\n  border-radius: 18rpx;\n  color: #FFFFFF;\n  font-size: 18rpx;\n  font-weight: bold;\n  text-align: center;\n  animation: pulse-animation 0.5s infinite ease-in-out;\n}\n\n.close-btn {\n  position: absolute;\n  top: -14rpx;\n  right: -14rpx;\n  width: 28rpx;\n  height: 28rpx;\n  background-color: #888888;\n  color: #fff;\n  border-radius: 50%;\n  border: 2rpx solid #fff;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 20rpx;\n  line-height: 28rpx;\n  z-index: 1000;\n}\n\n.page {\n  background-color: #599EFF;\n  min-height: 100vh;\n  padding-bottom: 80rpx;\n}\n\n.content {\n  width: 100%;\n  height: 100%;\n  background-color: #fff;\n  border-radius: 40rpx 40rpx 0 0;\n  padding: 0 30rpx;\n  padding-top: 18rpx;\n  padding-bottom: 50rpx;\n  overflow: auto;\n}\n\n.search_position {\n  width: 630rpx;\n  height: 72rpx;\n  background: #F1F1F1;\n  border-radius: 36rpx;\n  display: flex;\n  align-items: center;\n  padding: 0 15rpx;\n  position: relative;\n}\n\n.position {\n  font-size: 28rpx;\n  color: #333333;\n  margin: 0 12rpx;\n}\n\n.shu {\n  margin: 0 15rpx;\n  color: rgba(0, 0, 0, 0.2);\n}\n\ninput {\n  margin-left: 22rpx;\n  font-size: 28rpx;\n  color: #ADADAD;\n  width: 260rpx;\n}\n\n.btn {\n  width: 112rpx;\n  height: 56rpx;\n  background: #2E80FE;\n  border-radius: 28rpx;\n  font-size: 28rpx;\n  color: #FFFFFF;\n  line-height: 56rpx;\n  text-align: center;\n  position: absolute;\n  right: 20rpx;\n}\n\n.img {\n  margin-top: 20rpx;\n}\n\n.tag {\n  display: flex;\n  justify-content: space-around;\n  color: #BDD4FD;\n  margin-top: 10rpx;\n}\n\n.tag_item {\n  display: flex;\n  align-items: center;\n}\n\n.tag_item text {\n  font-size: 24rpx;\n  color: #333333;\n  margin-left: 14rpx;\n}\n\n.grid {\n  margin-top: 40rpx;\n}\n\n.grid-container {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: space-between;\n}\n\n.grid-item {\n  width: 20%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 10rpx 0;\n}\n\n.swiper {\n  width: 690rpx;\n  height: 90rpx;\n  margin-top: 40rpx;\n  display: flex;\n  align-items: center;\n  padding: 0 32rpx;\n  border-radius: 16rpx;\n  border: 2rpx solid #F7F6F6;\n}\n\n.swiper span {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #000;\n}\n\n.swiper span text {\n  color: #E72427;\n}\n\n.swiper .shu {\n  font-weight: 200;\n  color: #E6E6E6;\n  margin-left: 20rpx;\n}\n.welfare {\n  margin-top: 38rpx;\n  width: 690rpx;\n  height: 325rpx;\n  background-image: url(\"../static/images/9243.png\");\n  background-size: cover;\n  padding: 22rpx;\n  position: relative;\n}\n\n.welfare .top {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  width: 100%;\n}\n\n.welfare .top .left {\n  display: flex;\n  align-items: center;\n  flex: 1;\n}\n\n.welfare .top .left text {\n  margin-left: 16rpx;\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #451815;\n}\n\n.welfare .top .btn {\n  width: 124rpx;\n  height: 46rpx;\n  background: linear-gradient(270deg, #EA5533 0%, #EE8751 100%);\n  border-radius: 24rpx;\n  font-size: 20rpx;\n  color: #FFFFFF;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 14rpx;\n  flex-shrink: 0;\n}\n\n.welfare .bottom {\n  width: 646rpx;\n  margin-top: 12rpx;\n  display: flex;\n  border-radius: 12rpx;\n  background-color: #fff;\n}\n\n.welfare .bottom .right {\n  flex: 1;\n  display: flex;\n  justify-content: space-around;\n  align-items: center;\n}\n\n.welfare .bottom .right .right_item {\n  width: 136rpx;\n  height: 198rpx;\n  background: linear-gradient(180deg, #FEF7EC 0%, #FCEFDF 100%);\n  border-radius: 12rpx;\n  padding-top: 28rpx;\n}\n\n.welfare .bottom .right .right_item .box1 {\n  font-size: 44rpx;\n  font-weight: 600;\n  color: #E55138;\n  text-align: center;\n}\n\n.welfare .bottom .right .right_item .box1 span {\n  font-size: 24rpx;\n}\n\n.welfare .bottom .right .right_item .box2 {\n  margin-top: 12rpx;\n  font-size: 24rpx;\n  color: #E55138;\n  text-align: center;\n}\n\n.welfare .bottom .right .right_item .box3 {\n  margin: 12rpx auto 0;\n  width: 98rpx;\n  height: 36rpx;\n  background: linear-gradient(270deg, #EA5533 0%, #EE8751 100%);\n  border-radius: 18rpx;\n  font-size: 16rpx;\n  color: #FFFFFF;\n  line-height: 36rpx;\n  text-align: center;\n}\n.service {\n  margin-top: 40rpx;\n}\n\n.service .head {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.service .head .left {\n  font-size: 36rpx;\n  font-weight: 500;\n  color: #333333;\n}\n\n.service .head .right {\n  font-size: 24rpx;\n  color: #999999;\n  display: flex;\n  align-items: center;\n}\n\n.service .se_main {\n  display: flex;\n  justify-content: space-between;\n  flex-wrap: wrap;\n  margin-top: 20rpx;\n}\n\n.service .se_main .se_item {\n  width: 336rpx;\n  height: 428rpx;\n  background: #FFFFFF;\n  border-radius: 20rpx;\n  border: 2rpx solid #F3F3F3;\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: center;\n  padding-top: 20rpx;\n  margin-bottom: 20rpx;\n}\n\n.service .se_main .se_item .lbox {\n  margin-top: 10rpx;\n}\n\n.service .se_main .se_item .lbox .name {\n  font-size: 32rpx;\n  font-weight: 500;\n  color: #333333;\n  text-align: center;\n}\n\n.service .se_main .se_item .lbox .baojia {\n  margin: 6rpx auto 0;\n  width: 140rpx;\n  height: 50rpx;\n  background: #FFFFFF;\n  border-radius: 8rpx;\n  border: 2rpx solid #2E80FE;\n  font-size: 24rpx;\n  color: #2E80FE;\n  line-height: 50rpx;\n  text-align: center;\n}\n\n.tips {\n  margin-top: 32rpx;\n  font-size: 36rpx;\n  font-weight: 500;\n  color: #333333;\n  text-align: center;\n}\n\n.tips2 {\n  font-size: 24rpx;\n  color: #333333;\n  margin-top: 16rpx;\n  text-align: center;\n}\n</style>", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./service.vue?vue&type=style&index=0&id=fe340868&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./service.vue?vue&type=style&index=0&id=fe340868&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755161080126\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}