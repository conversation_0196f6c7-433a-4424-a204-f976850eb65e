{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/node_modules/uview-ui/components/u-scroll-list/u-scroll-list.vue?1e06", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/node_modules/uview-ui/components/u-scroll-list/u-scroll-list.vue?9f7f", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/node_modules/uview-ui/components/u-scroll-list/u-scroll-list.vue?925c", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/node_modules/uview-ui/components/u-scroll-list/u-scroll-list.vue?19f2", "uni-app:///node_modules/uview-ui/components/u-scroll-list/u-scroll-list.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/node_modules/uview-ui/components/u-scroll-list/u-scroll-list.vue?1780", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/node_modules/uview-ui/components/u-scroll-list/u-scroll-list.vue?7b2d", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/node_modules/uview-ui/components/u-scroll-list/scrollWxs.wxs?5f87", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/node_modules/uview-ui/components/u-scroll-list/scrollWxs.wxs?920a"], "names": ["name", "mixins", "data", "scrollInfo", "scrollLeft", "scrollWidth", "computed", "barStyle", "style", "lineStyle", "mounted", "methods", "init", "scrollEvent", "getComponentWidth", "uni"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkU;AAClU;AACiE;AACL;AACsC;;;AAGlG;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,gSAAM;AACR,EAAE,ySAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oSAAU;AACZ;AACA;;AAEA;AACoR;AACpR,WAAW,qSAAM,iBAAiB,6SAAM;;AAExC;AACe,gF;;;;;;;;;;;;AC3Bf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3BA;AAAA;AAAA;AAAA;AAA41B,CAAgB,42BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACmGh3B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAlBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAbA,eAmBA;EACAA;EACAC;EAIAC;IACA;MACAC;QACAC;QACAC;MACA;MACAA;IACA;EACA;EACAC;IACA;IACAC;MACA;;MAWA;MACAC;MACAA;MACA;IACA;IACAC;MACA;MACA;MACAD;MACAA;MACA;IACA;EACA;EACAE;IACA;EACA;EACAC;IACAC;MACA;IACA;IAiBA;IACAC;MACA;IACA;IACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEAC;cAAA;gBAEA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IASA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC3LA;AAAA;AAAA;AAAA;AAAmmD,CAAgB,ujDAAG,EAAC,C;;;;;;;;;;;ACAvnD;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA,wCAAoc,CAAgB,ugBAAG,EAAC,C;;;;;;;;;;;;ACAxd;AAAe;AACf;AACA;AACA;AACA;AACA,M", "file": "node-modules/uview-ui/components/u-scroll-list/u-scroll-list.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-scroll-list.vue?vue&type=template&id=e2a26316&scoped=true&filter-modules=eyJ3eHMiOnsidHlwZSI6InNjcmlwdCIsImNvbnRlbnQiOiIiLCJzdGFydCI6MTg5OCwiYXR0cnMiOnsic3JjIjoiLi9zY3JvbGxXeHMud3hzIiwibW9kdWxlIjoid3hzIiwibGFuZyI6Ind4cyJ9LCJlbmQiOjE4OTh9fQ%3D%3D&\"\nvar renderjs\nimport script from \"./u-scroll-list.vue?vue&type=script&lang=js&\"\nexport * from \"./u-scroll-list.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-scroll-list.vue?vue&type=style&index=0&id=e2a26316&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"e2a26316\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\n/* custom blocks */\nimport block0 from \"./scrollWxs.wxs?vue&type=custom&index=0&blockType=script&issuerPath=C%3A%5CUsers%5CAdministrator%5CDesktop%5CAPP%20(2)%5C8-5APP%5CAPP%E5%B8%88%E5%82%85%E7%AB%AF%5Cnode_modules%5Cuview-ui%5Ccomponents%5Cu-scroll-list%5Cu-scroll-list.vue&module=wxs&lang=wxs\"\nif (typeof block0 === 'function') block0(component)\n\ncomponent.options.__file = \"node_modules/uview-ui/components/u-scroll-list/u-scroll-list.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-scroll-list.vue?vue&type=template&id=e2a26316&scoped=true&filter-modules=eyJ3eHMiOnsidHlwZSI6InNjcmlwdCIsImNvbnRlbnQiOiIiLCJzdGFydCI6MTg5OCwiYXR0cnMiOnsic3JjIjoiLi9zY3JvbGxXeHMud3hzIiwibW9kdWxlIjoid3hzIiwibGFuZyI6Ind4cyJ9LCJlbmQiOjE4OTh9fQ%3D%3D&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.$u.getPx(_vm.indicatorBarWidth)\n  var g1 = _vm.$u.getPx(_vm.indicatorWidth)\n  var s0 = _vm.indicator\n    ? _vm.__get_style([_vm.$u.addStyle(_vm.indicatorStyle)])\n    : null\n  var s1 = _vm.indicator ? _vm.__get_style([_vm.lineStyle]) : null\n  var s2 = _vm.indicator ? _vm.__get_style([_vm.barStyle]) : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        s0: s0,\n        s1: s1,\n        s2: s2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-scroll-list.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-scroll-list.vue?vue&type=script&lang=js&\"", "<template>\n\t<view\n\t\tclass=\"u-scroll-list\"\n\t\tref=\"u-scroll-list\"\n\t>\n\t\t<!-- #ifdef APP-NVUE -->\n\t\t<!-- nvue使用bindingX实现，以得到更好的性能 -->\n\t\t<scroller\n\t\t\tclass=\"u-scroll-list__scroll-view\"\n\t\t\tref=\"u-scroll-list__scroll-view\"\n\t\t\tscroll-direction=\"horizontal\"\n\t\t\t:show-scrollbar=\"false\"\n\t\t\t:offset-accuracy=\"1\"\n\t\t\t@scroll=\"nvueScrollHandler\"\n\t\t>\n\t\t\t<view class=\"u-scroll-list__scroll-view__content\">\n\t\t\t\t<slot />\n\t\t\t</view>\n\t\t</scroller>\n\t\t<!-- #endif -->\n\t\t<!-- #ifndef APP-NVUE -->\n\t\t<!-- #ifdef MP-WEIXIN || APP-VUE || H5 || MP-QQ -->\n\t\t<!-- 以上平台，支持wxs -->\n\t\t<scroll-view\n\t\t\tclass=\"u-scroll-list__scroll-view\"\n\t\t\tscroll-x\n\t\t\t@scroll=\"wxs.scroll\"\n\t\t\t@scrolltoupper=\"wxs.scrolltoupper\"\n\t\t\t@scrolltolower=\"wxs.scrolltolower\"\n\t\t\t:data-scrollWidth=\"scrollWidth\"\n\t\t\t:data-barWidth=\"$u.getPx(indicatorBarWidth)\"\n\t\t\t:data-indicatorWidth=\"$u.getPx(indicatorWidth)\"\n\t\t\t:show-scrollbar=\"false\"\n\t\t\t:upper-threshold=\"0\"\n\t\t\t:lower-threshold=\"0\"\n\t\t>\n\t\t\t<!-- #endif -->\n\t\t\t<!-- #ifndef APP-NVUE || MP-WEIXIN || H5 || APP-VUE || MP-QQ -->\n\t\t\t<!-- 非以上平台，只能使用普通js实现 -->\n\t\t\t<scroll-view\n\t\t\t\tclass=\"u-scroll-list__scroll-view\"\n\t\t\t\tscroll-x\n\t\t\t\t@scroll=\"scrollHandler\"\n\t\t\t\t@scrolltoupper=\"scrolltoupperHandler\"\n\t\t\t\t@scrolltolower=\"scrolltolowerHandler\"\n\t\t\t\t:show-scrollbar=\"false\"\n\t\t\t\t:upper-threshold=\"0\"\n\t\t\t\t:lower-threshold=\"0\"\n\t\t\t>\n\t\t\t\t<!-- #endif -->\n\t\t\t\t<view class=\"u-scroll-list__scroll-view__content\">\n\t\t\t\t\t<slot />\n\t\t\t\t</view>\n\t\t\t</scroll-view>\n\t\t\t<!-- #endif -->\n\t\t\t<view\n\t\t\t\tclass=\"u-scroll-list__indicator\"\n\t\t\t\tv-if=\"indicator\"\n\t\t\t\t:style=\"[$u.addStyle(indicatorStyle)]\"\n\t\t\t>\n\t\t\t\t<view\n\t\t\t\t\tclass=\"u-scroll-list__indicator__line\"\n\t\t\t\t\t:style=\"[lineStyle]\"\n\t\t\t\t>\n\t\t\t\t\t<view\n\t\t\t\t\t\tclass=\"u-scroll-list__indicator__line__bar\"\n\t\t\t\t\t\t:style=\"[barStyle]\"\n\t\t\t\t\t\tref=\"u-scroll-list__indicator__line__bar\"\n\t\t\t\t\t></view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t</view>\n</template>\n\n<script\n\tsrc=\"./scrollWxs.wxs\"\n\tmodule=\"wxs\"\n\tlang=\"wxs\"\n></script>\n\n<script>\n/**\n * scrollList 横向滚动列表\n * @description 该组件一般用于同时展示多个商品、分类的场景，也可以完成左右滑动的列表。\n * @tutorial https://www.uviewui.com/components/scrollList.html\n * @property {String | Number}\tindicatorWidth\t\t\t指示器的整体宽度 (默认 50 )\n * @property {String | Number}\tindicatorBarWidth\t\t滑块的宽度 (默认 20 )\n * @property {Boolean}\t\t\tindicator\t\t\t\t是否显示面板指示器 (默认 true )\n * @property {String}\t\t\tindicatorColor\t\t\t指示器非激活颜色 (默认 '#f2f2f2' )\n * @property {String}\t\t\tindicatorActiveColor\t指示器的激活颜色 (默认 '#3c9cff' )\n * @property {String | Object}\tindicatorStyle\t\t\t指示器样式，可通过bottom，left，right进行定位\n * @event {Function} left\t滑动到左边时触发\n * @event {Function} right\t滑动到右边时触发\n * @example\n */\n// #ifdef APP-NVUE\nconst dom = uni.requireNativePlugin('dom')\nimport nvueMixin from \"./nvue.js\"\n// #endif\nimport props from './props.js';\nexport default {\n\tname: 'u-scroll-list',\n\tmixins: [uni.$u.mpMixin, uni.$u.mixin, props],\n\t// #ifdef APP-NVUE\n\tmixins: [uni.$u.mpMixin, uni.$u.mixin, nvueMixin, props],\n\t// #endif\n\tdata() {\n\t\treturn {\n\t\t\tscrollInfo: {\n\t\t\t\tscrollLeft: 0,\n\t\t\t\tscrollWidth: 0\n\t\t\t},\n\t\t\tscrollWidth: 0\n\t\t}\n\t},\n\tcomputed: {\n\t\t// 指示器为线型的样式\n\t\tbarStyle() {\n\t\t\tconst style = {}\n\t\t\t// #ifndef APP-NVUE || MP-WEIXIN || H5 || APP-VUE || MP-QQ\n\t\t\t// 此为普通js方案，只有在非nvue和不支持wxs方案的端才使用、\n\t\t\t// 此处的计算理由为：scroll-view的滚动距离与目标滚动距离(scroll-view的实际宽度减去包裹元素的宽度)之比，等于滑块当前移动距离与总需\n\t\t\t// 滑动距离(指示器的总宽度减去滑块宽度)的比值\n\t\t\tconst scrollLeft = this.scrollInfo.scrollLeft,\n\t\t\t\tscrollWidth = this.scrollInfo.scrollWidth,\n\t\t\t\tbarAllMoveWidth = this.indicatorWidth - this.indicatorBarWidth\n\t\t\tconst x = scrollLeft / (scrollWidth - this.scrollWidth) * barAllMoveWidth\n\t\t\tstyle.transform = `translateX(${ x }px)`\n\t\t\t// #endif\n\t\t\t// 设置滑块的宽度和背景色，是每个平台都需要的\n\t\t\tstyle.width = uni.$u.addUnit(this.indicatorBarWidth)\n\t\t\tstyle.backgroundColor = this.indicatorActiveColor\n\t\t\treturn style\n\t\t},\n\t\tlineStyle() {\n\t\t\tconst style = {}\n\t\t\t// 指示器整体的样式，需要设置其宽度和背景色\n\t\t\tstyle.width = uni.$u.addUnit(this.indicatorWidth)\n\t\t\tstyle.backgroundColor = this.indicatorColor\n\t\t\treturn style\n\t\t}\n\t},\n\tmounted() {\n\t\tthis.init()\n\t},\n\tmethods: {\n\t\tinit() {\n\t\t\tthis.getComponentWidth()\n\t\t},\n\t\t// #ifndef APP-NVUE || MP-WEIXIN || H5 || APP-VUE || MP-QQ\n\t\t// scroll-view触发滚动事件\n\t\tscrollHandler(e) {\n\t\t\tthis.scrollInfo = e.detail\n\t\t},\n\t\tscrolltoupperHandler() {\n\t\t\tthis.scrollEvent('left')\n\t\t\tthis.scrollInfo.scrollLeft = 0\n\t\t},\n\t\tscrolltolowerHandler() {\n\t\t\tthis.scrollEvent('right')\n\t\t\t// 在普通js方案中，滚动到右边时，通过设置this.scrollInfo，模拟出滚动到右边的情况\n\t\t\t// 因为上方是用过computed计算的，设置后，会自动调整滑块的位置\n\t\t\tthis.scrollInfo.scrollLeft = uni.$u.getPx(this.indicatorWidth) - uni.$u.getPx(this.indicatorBarWidth)\n\t\t},\n\t\t// #endif\n\t\t//\n\t\tscrollEvent(status) {\n\t\t\tthis.$emit(status)\n\t\t},\n\t\t// 获取组件的宽度\n\t\tasync getComponentWidth() {\n\t\t\t// 延时一定时间，以获取dom尺寸\n\t\t\tawait uni.$u.sleep(30)\n\t\t\t// #ifndef APP-NVUE\n\t\t\tthis.$uGetRect('.u-scroll-list').then(size => {\n\t\t\t\tthis.scrollWidth = size.width\n\t\t\t})\n\t\t\t// #endif\n\n\t\t\t// #ifdef APP-NVUE\n\t\t\tconst ref = this.$refs['u-scroll-list']\n\t\t\tref && dom.getComponentRect(ref, (res) => {\n\t\t\t\tthis.scrollWidth = res.size.width\n\t\t\t})\n\t\t\t// #endif\n\t\t},\n\t}\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import \"../../libs/css/components.scss\";\n\n.u-scroll-list {\n\tpadding-bottom: 10px;\n\n\t&__scroll-view {\n\t\t@include flex;\n\n\t\t&__content {\n\t\t\t@include flex;\n\t\t}\n\t}\n\n\t&__indicator {\n\t\t@include flex;\n\t\tjustify-content: center;\n\t\tmargin-top: 15px;\n\n\t\t&__line {\n\t\t\twidth: 60px;\n\t\t\theight: 4px;\n\t\t\tborder-radius: 100px;\n\t\t\toverflow: hidden;\n\n\t\t\t&__bar {\n\t\t\t\twidth: 20px;\n\t\t\t\theight: 4px;\n\t\t\t\tborder-radius: 100px;\n\t\t\t}\n\t\t}\n\t}\n}\n</style>\n", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-scroll-list.vue?vue&type=style&index=0&id=e2a26316&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-scroll-list.vue?vue&type=style&index=0&id=e2a26316&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755161086673\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-filter-loader\\\\index.js!./scrollWxs.wxs?vue&type=custom&index=0&blockType=script&issuerPath=C%3A%5CUsers%5CAdministrator%5CDesktop%5CAPP%20(2)%5C8-5APP%5CAPP%E5%B8%88%E5%82%85%E7%AB%AF%5Cnode_modules%5Cuview-ui%5Ccomponents%5Cu-scroll-list%5Cu-scroll-list.vue&module=wxs&lang=wxs\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-filter-loader\\\\index.js!./scrollWxs.wxs?vue&type=custom&index=0&blockType=script&issuerPath=C%3A%5CUsers%5CAdministrator%5CDesktop%5CAPP%20(2)%5C8-5APP%5CAPP%E5%B8%88%E5%82%85%E7%AB%AF%5Cnode_modules%5Cuview-ui%5Ccomponents%5Cu-scroll-list%5Cu-scroll-list.vue&module=wxs&lang=wxs\"", "export default function (Component) {\n       if(!Component.options.wxsCallMethods){\n         Component.options.wxsCallMethods = []\n       }\n       Component.options.wxsCallMethods.push('scrollEvent')\n     }"], "sourceRoot": ""}